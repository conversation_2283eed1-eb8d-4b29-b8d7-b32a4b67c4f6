import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import Card from "@mui/material/Card";
import { Dialog, DialogTitle, DialogContent, DialogActions, Breadcrumbs, Chip } from "@mui/material";
import FolderIcon from "@mui/icons-material/Folder";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import HomeIcon from "@mui/icons-material/Home";
import MDBox from "components/atoms/MDBox";
import MDButton from "components/atoms/MDButton";
import MDInput from "components/atoms/MDInput";
import MDTypography from "components/atoms/MDTypography";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import DataTable from "examples/Tables/DataTable";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import { AssignmentTableHeader } from "constants/AssignmentTableHeader/AssignmentTableHeader";
import { TableInfo } from "components/molecules/TableInfo/TableInfo";

export function Assignments() {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  useEffect(() => {
    if (!loading && !apiService) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading]);
  const [allAssignments, setAllAssignments] = useState([]);
  const [allFolders, setAllFolders] = useState([]);
  const [tableData, setTableData] = useState({});
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Folder navigation state
  const [currentFolderId, setCurrentFolderId] = useState(null);
  const [breadcrumbPath, setBreadcrumbPath] = useState([]);

  // Create folder modal state
  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  const navigate = useNavigate();

  const FOLDERS_DATA = [
    {
      "id": "folder_001",
      "name": "Term 1 - Physics Assignments",
      "userId": "user_123",
      "createdAt": "2025-06-15T10:30:00Z",
    },
    {
      "id": "folder_002",
      "name": "Math Practice Papers",
      "userId": "user_123",
      "createdAt": "2025-06-16T14:45:00Z",
    },
    {
      "id": "folder_003",
      "name": "Informatics Final Review",
      "userId": "user_123",
      "createdAt": "2025-06-17T09:00:00Z",
    },
  ];

  const ASSIGNMENTS_DATA = [
    {
      "class": 12,
      "id": "d212f413-a7ad-474e-aafd-8f70cc11a96c",
      "name": "DAV IP Test",
      "questions": null,
      "sectionList": ["A"],
      "subjectName": "Informatics Practices",
      "totalScore": 20,
      "folderId": "folder_001",
    },
    {
      "class": 10,
      "id": "2faed4b3-3e42-4f1f-bc78-dcc5b23d002d",
      "name": "ILS Maths Prelims",
      "questions": null,
      "sectionList": ["A"],
      "subjectName": "Mathematics",
      "totalScore": 80,
      "folderId": null,
    },
    {
      "class": 12,
      "id": "07183f1b-b124-4d69-8da1-23b3556d2d87",
      "name": "Amalkul Physics",
      "questions": null,
      "sectionList": ["A"],
      "subjectName": "Physics",
      "totalScore": 35,
      "folderId": "folder_001",
    },
  ];

  useEffect(() => {
    const fetchAndSetData = async () => {
      try {
        // const result = await apiService.getAllAssignments();
        // const foldersResult = await apiService.getAllFolders();

        // setAllAssignments(result);
        // setAllFolders(foldersResult);
        setAllAssignments(ASSIGNMENTS_DATA);
        setAllFolders(FOLDERS_DATA);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchAndSetData();
  }, [apiService]);

  // Custom table header that includes folder icon for folders
  const getTableColumns = () => {
    return AssignmentTableHeader.map(column => {
      if (column.accessor === 'name') {
        return {
          ...column,
          Cell: ({ row }) => {
            const isFolder = row.original.isFolder;
            return (
              <MDBox display="flex" alignItems="center" gap={1}>
                {isFolder && <FolderIcon color="primary" />}
                <MDTypography variant="button" fontWeight="medium">
                  {row.original.name}
                </MDTypography>
              </MDBox>
            );
          }
        };
      }
      return column;
    });
  };

  // Prepare table data based on current folder
  useEffect(() => {
    const prepareTableData = () => {
      if (currentFolderId === null) {
        // Show folders and assignments without folderId
        const foldersAsRows = allFolders.map(folder => ({
          ...folder,
          isFolder: true,
          name: folder.name,
          class: "Folder",
          subjectName: "—",
          totalScore: "—",
          sectionList: ["—"]
        }));

        const unassignedAssignments = allAssignments.filter(assignment =>
          assignment.folderId === null
        );

        const combinedRows = [...foldersAsRows, ...unassignedAssignments];

        setTableData({
          columns: getTableColumns(),
          rows: combinedRows,
        });
      } else {
        // Show assignments in the current folder
        const folderAssignments = allAssignments.filter(assignment =>
          assignment.folderId === currentFolderId
        );

        setTableData({
          columns: AssignmentTableHeader,
          rows: folderAssignments,
        });
      }
    };

    prepareTableData();
  }, [allAssignments, allFolders, currentFolderId]);

  const handleNavigateToGradeAssignment = (data) => {
    if (data?.isFolder) {
      // Navigate into folder
      handleFolderClick(data);
    } else {
      navigate(`/assignments/${data?.id}?operation=grade`);
    }
  };

  const handleFolderClick = (folder) => {
    setCurrentFolderId(folder.id);
    setBreadcrumbPath([...breadcrumbPath, { id: folder.id, name: folder.name }]);
  };

  const handleBackClick = () => {
    if (breadcrumbPath.length > 0) {
      const newPath = [...breadcrumbPath];
      newPath.pop();
      setBreadcrumbPath(newPath);

      if (newPath.length === 0) {
        setCurrentFolderId(null);
      } else {
        setCurrentFolderId(newPath[newPath.length - 1].id);
      }
    }
  };

  const handleBreadcrumbClick = (index) => {
    if (index === -1) {
      // Clicked on "All Assignments"
      setCurrentFolderId(null);
      setBreadcrumbPath([]);
    } else {
      // Clicked on a folder in breadcrumb
      const newPath = breadcrumbPath.slice(0, index + 1);
      setBreadcrumbPath(newPath);
      setCurrentFolderId(newPath[newPath.length - 1].id);
    }
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    setIsCreatingFolder(true);
    try {
      // In real implementation, call API to create folder
      // const newFolder = await apiService.createFolder({ name: newFolderName.trim() });

      // For now, simulate folder creation
      const newFolder = {
        id: `folder_${Date.now()}`,
        name: newFolderName.trim(),
        userId: "user_123",
        createdAt: new Date().toISOString(),
      };

      setAllFolders([...allFolders, newFolder]);
      setNewFolderName("");
      setIsCreateFolderModalOpen(false);
    } catch (err) {
      console.error("Error creating folder:", err);
      // Handle error (show toast, etc.)
    } finally {
      setIsCreatingFolder(false);
    }
  };

  const handleCancelCreateFolder = () => {
    setNewFolderName("");
    setIsCreateFolderModalOpen(false);
  };

  if (isLoading) {
    return <Loader fullScreen message="Don't Reload Page" />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText="All Assignments" />

      {/* Create Folder Modal */}
      <Dialog
        open={isCreateFolderModalOpen}
        onClose={handleCancelCreateFolder}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <MDTypography variant="h5" color="dark">
            Create New Folder
          </MDTypography>
        </DialogTitle>
        <DialogContent>
          <MDBox mt={2}>
            <MDInput
              label="Folder Name"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              fullWidth
              variant="standard"
              onKeyPress={(e) => {
                if (e.key === 'Enter' && newFolderName.trim()) {
                  handleCreateFolder();
                }
              }}
            />
          </MDBox>
        </DialogContent>
        <DialogActions>
          <MDBox
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
            px={0.5}
          >
            <MDButton
              variant="outlined"
              color="dark"
              onClick={handleCancelCreateFolder}
              disabled={isCreatingFolder}
            >
              Cancel
            </MDButton>
            <MDButton
              variant="gradient"
              color="info"
              onClick={handleCreateFolder}
              disabled={isCreatingFolder || !newFolderName.trim()}
              sx={{ width: "8rem" }}
            >
              {isCreatingFolder ? <Loader size={20} /> : "Create Folder"}
            </MDButton>
          </MDBox>
        </DialogActions>
      </Dialog>

      <MDBox py={1}>
        <MDBox mb={3}>
          <Card>
            {/* Breadcrumb Navigation */}
            {(currentFolderId !== null || breadcrumbPath.length > 0) && (
              <MDBox px={3} pt={2.5} pb={1}>
                <MDBox display="flex" alignItems="center" gap={1} mb={1}>
                  <MDButton
                    variant="outlined"
                    color="dark"
                    size="small"
                    onClick={handleBackClick}
                    sx={{ minWidth: "auto", px: 1 }}
                  >
                    <ArrowBackIcon fontSize="small" />
                  </MDButton>
                  <Breadcrumbs separator="›" sx={{ ml: 1 }}>
                    <Chip
                      icon={<HomeIcon />}
                      label="All Assignments"
                      onClick={() => handleBreadcrumbClick(-1)}
                      variant="outlined"
                      size="small"
                      sx={{ cursor: "pointer" }}
                    />
                    {breadcrumbPath.map((folder, index) => (
                      <Chip
                        key={folder.id}
                        icon={<FolderIcon />}
                        label={folder.name}
                        onClick={() => handleBreadcrumbClick(index)}
                        variant={index === breadcrumbPath.length - 1 ? "filled" : "outlined"}
                        size="small"
                        sx={{ cursor: "pointer" }}
                      />
                    ))}
                  </Breadcrumbs>
                </MDBox>
              </MDBox>
            )}

            <TableInfo
              tableTitle={currentFolderId ? `${breadcrumbPath[breadcrumbPath.length - 1]?.name || 'Folder'} Contents` : "Assignments & Folders"}
              tableDesc={currentFolderId ? "Assignments in this folder" : "Click on folders to navigate or assignments to grade"}
              buttonText="Create New Assignment"
              handleClick={() => navigate(`/assignments?operation=new`)}
              secButtonText={currentFolderId === null ? "Create Folder" : ""}
              handleSecClick={() => setIsCreateFolderModalOpen(true)}
            />
            <DataTable
              table={tableData}
              canSearch
              viewData
              onView={(assignment) =>
                navigate(`/assignments/${assignment.value}?operation=view`)
              }
              isRowClickable
              handleRowClick={handleNavigateToGradeAssignment}
              grades
              onGrade={(submission) => {
                navigate(`/assignments/${submission.value}?operation=grade`);
              }}
            />
          </Card>
        </MDBox>
      </MDBox>
    </DashboardLayout>
  );
}
