import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import Card from "@mui/material/Card";
import MDBox from "components/atoms/MDBox";
import DashboardLayout from "examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "examples/Navbars/DashboardNavbar";
import DataTable from "examples/Tables/DataTable";
import Loader from "components/atoms/Loader/Loader";
import { ApiServiceContext } from "context";
import { AssignmentTableHeader } from "constants/AssignmentTableHeader/AssignmentTableHeader";
import { TableInfo } from "components/molecules/TableInfo/TableInfo";

export function Assignments() {
  const { apiService, loading, handleLogin } = useContext(ApiServiceContext);
  useEffect(() => {
    if (!loading && !apiService) {
      handleLogin(window.location.href);
    }
  }, [apiService, loading]);
  const [allAssignments, setAllAssignments] = useState([]);
  const [tableData, setTableData] = useState({});
  const [isLoading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const navigate = useNavigate();

  const FOLDERS_DATA = [
    {
      "id": "folder_001",
      "name": "Term 1 - Physics Assignments",
      "userId": "user_123",
      "createdAt": "2025-06-15T10:30:00Z",
    },
    {
      "id": "folder_002",
      "name": "Math Practice Papers",
      "userId": "user_123",
      "createdAt": "2025-06-16T14:45:00Z",
    },
    {
      "id": "folder_003",
      "name": "Informatics Final Review",
      "userId": "user_123",
      "createdAt": "2025-06-17T09:00:00Z",
    },
  ];

  const ASSIGNMENT_DATA = [
    {
      "class": 12,
      "id": "d212f413-a7ad-474e-aafd-8f70cc11a96c",
      "name": "DAV IP Test",
      "questions": null,
      "sectionList": ["A"],
      "subjectName": "Informatics Practices",
      "totalScore": 20,
      "folderId": "folder_001",
    },
    {
      "class": 10,
      "id": "2faed4b3-3e42-4f1f-bc78-dcc5b23d002d",
      "name": "ILS Maths Prelims",
      "questions": null,
      "sectionList": ["A"],
      "subjectName": "Mathematics",
      "totalScore": 80,
      "folderId": null,
    },
    {
      "class": 12,
      "id": "07183f1b-b124-4d69-8da1-23b3556d2d87",
      "name": "Amalkul Physics",
      "questions": null,
      "sectionList": ["A"],
      "subjectName": "Physics",
      "totalScore": 35,
      "folderId": "folder_001",
    },
  ];

  useEffect(() => {
    const fetchAndSetAssignments = async () => {
      try {
        // const result = await apiService.getAllAssignments();

        // setAllAssignments(result);
        setAllAssignments(ASSIGNMENTS_DATA);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchAndSetAssignments();
  }, [apiService]);

  useEffect(() => {
    setTableData({
      columns: AssignmentTableHeader,
      rows: allAssignments,
    });
  }, [allAssignments]);

  const handleNavigateToGradeAssignment = (data) => {
    navigate(`/assignments/${data?.id}?operation=grade`);
  };

  if (isLoading) {
    return <Loader fullScreen message="Don't Reload Page" />;
  }

  return (
    <DashboardLayout>
      <DashboardNavbar breadCrumbText="All Assignments" />
      <MDBox py={1}>
        <MDBox mb={3}>
          <Card>
            <TableInfo
              tableTitle="Assignments Data"
              tableDesc="Click on Assignment to Grade"
              buttonText="Create New Assignment"
              handleClick={() => navigate(`/assignments?operation=new`)}
            />
            <DataTable
              table={tableData}
              canSearch
              viewData
              onView={(assignment) =>
                navigate(`/assignments/${assignment.value}?operation=view`)
              }
              isRowClickable
              handleRowClick={handleNavigateToGradeAssignment}
              grades
              onGrade={(submission) => {
                navigate(`/assignments/${submission.value}?operation=grade`);
              }}
            />
          </Card>
        </MDBox>
      </MDBox>
    </DashboardLayout>
  );
}
